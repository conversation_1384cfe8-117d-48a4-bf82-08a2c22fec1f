import { Ionicons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import React, { useState } from 'react';
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    ScrollView,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function SignIn() {
  const { t } = useLanguage();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Test@1234');
  const [rememberMe, setRememberMe] = useState(true);
  const [showPassword, setShowPassword] = useState(false);

  const handleSignIn = () => {
    // Handle sign in logic here
    console.log('Sign in with:', { email, password, rememberMe });
    // Navigate to main app
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 px-6 pt-16">
            {/* Logo */}
            <View className="items-center mb-12">
              <Image
                source={require('../assets/images/logo.png')}
                className="w-40 h-16"
                resizeMode="contain"
              />
            </View>

            {/* Welcome Text */}
            <View className="mb-8">
              <Text className="text-2xl font-cairo-bold text-gray-900 mb-2">
                {t('auth.signin.title')}
              </Text>
              <Text className="text-base font-cairo-regular text-gray-500">
                {t('auth.signin.subtitle')}
              </Text>
            </View>

            {/* Email Input */}
            <View className="mb-4">
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder={t('auth.signin.email')}
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Password Input */}
            <View className="mb-4 relative">
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder={t('auth.signin.password')}
                placeholderTextColor="#9CA3AF"
                className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                secureTextEntry={!showPassword}
                autoComplete="password"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-4"
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </View>

            {/* Remember Me and Forgot Password */}
            <View className="flex-row items-center justify-between mb-8">
              <TouchableOpacity
                onPress={() => setRememberMe(!rememberMe)}
                className="flex-row items-center"
              >
                <View
                  className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                    rememberMe
                      ? 'bg-primary border-primary'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  {rememberMe && (
                    <Ionicons name="checkmark" size={12} color="white" />
                  )}
                </View>
                <Text className="font-cairo-regular text-gray-700">
                  {t('auth.signin.rememberMe')}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => router.push('/forgot-password' as any)}>
                <Text className="font-cairo-regular text-primary">
                  {t('auth.signin.forgotPassword')}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Sign In Button */}
            <TouchableOpacity
              onPress={handleSignIn}
              className="w-full h-14 bg-primary rounded-lg items-center justify-center mb-8"
            >
              <Text className="font-cairo-semibold text-lg text-black">
                {t('auth.signin.signInButton')}
              </Text>
            </TouchableOpacity>

            {/* Divider */}
            <View className="flex-row items-center mb-8">
              <View className="flex-1 h-px bg-gray-300" />
              <Text className="mx-4 font-cairo-regular text-gray-500">
                {t('auth.signin.orSignInWith')}
              </Text>
              <View className="flex-1 h-px bg-gray-300" />
            </View>

            {/* Terms and Conditions */}
            <View className="items-center mb-6">
              <Text className="font-cairo-regular text-gray-500 text-center">
                {t('auth.signin.termsText')}{' '}
                <Text className="text-black font-cairo-medium">
                  {t('auth.signin.termsLink')}
                </Text>{' '}
                {t('auth.signin.and')}{' '}
                <Text className="text-black font-cairo-medium">
                  {t('auth.signin.privacyLink')}
                </Text>
              </Text>
            </View>

            {/* Sign Up Link */}
            <View className="items-center">
              <Text className="font-cairo-regular text-gray-500">
                {t('auth.signin.newToSafqa')}{' '}
                <Link href="/signup" asChild>
                  <Text className="text-primary font-cairo-medium">
                    {t('auth.signin.joinSafqa')}
                  </Text>
                </Link>
              </Text>
            </View>
          </View>

          {/* Bottom Indicator */}
          <View className="items-center pb-8">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
