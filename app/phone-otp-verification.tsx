import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useRef, useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function PhoneOtpVerification() {
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  const phoneNumber = params.phoneNumber as string;
  const countryCode = params.countryCode as string;
  
  const [otp, setOtp] = useState(['', '', '', '']);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace') {
      if (!otp[index] && index > 0) {
        // If current field is empty, move to previous field and clear it
        const newOtp = [...otp];
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      } else if (otp[index]) {
        // If current field has value, clear it
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
      }
    }
  };

  const handleVerify = () => {
    const otpCode = otp.join('');
    if (otpCode.length === 4) {
      // Handle OTP verification logic here
      console.log('Verifying OTP:', otpCode);
      // Navigate to success modal
      router.push('/verification-success' as any);
    }
  };

  const handleResendOtp = () => {
    // Handle resend OTP logic here
    console.log('Resending OTP to:', countryCode + phoneNumber);
    setOtp(['', '', '', '']);
    inputRefs.current[0]?.focus();
  };

  const handleEditNumber = () => {
    router.back();
  };



  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View className="flex-row items-center justify-between px-6 py-4">
          <TouchableOpacity onPress={() => router.back()} className="p-2">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Logo */}
        <View className="items-center px-6 pb-8">
          <Image
            source={require('@/assets/images/logo.png')}
            className="w-48 h-16"
            resizeMode="contain"
          />
        </View>

        {/* Content */}
        <View className="flex-1 px-6">
          {/* Title */}
          <Text className="mb-4 text-2xl text-gray-900 font-cairo-bold">
            {t('auth.verification.phoneOtp.title')}
          </Text>

          {/* Subtitle with phone number */}
          <View className="mb-8">
            <Text className="mb-3 text-base text-gray-600 font-cairo-regular">
              {t('auth.verification.phoneOtp.subtitle')} <Text className="text-gray-900 font-cairo-semibold">{countryCode}{phoneNumber}</Text>
            </Text>
            <TouchableOpacity onPress={handleEditNumber} className="self-start">
              <Ionicons name="pencil" size={18} color="#F59E0B" />
            </TouchableOpacity>
          </View>

          {/* OTP Input Fields */}
          <View className="flex-row justify-center gap-3 mb-8">
            {otp.map((digit, index) => (
              <View key={index} className="relative">
                <TextInput
                  ref={(ref) => {
                    inputRefs.current[index] = ref;
                  }}
                  value={digit}
                  onChangeText={(value) => handleOtpChange(value.slice(-1), index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  className={`w-16 h-16 text-center text-2xl text-gray-900 border-2 rounded-lg font-cairo-semibold ${
                    digit ? 'border-primary bg-yellow-50' : 'border-gray-300 bg-gray-50'
                  }`}
                  keyboardType="numeric"
                  maxLength={1}
                  selectTextOnFocus
                />
              </View>
            ))}
          </View>

          {/* Resend OTP */}
          <View className="items-start mb-8">
            <Text className="text-sm text-gray-500 font-cairo-regular">
              {t('auth.verification.phoneOtp.haventReceived')}
              <Text
                onPress={handleResendOtp}
                className="text-sm text-primary font-cairo-medium"
              >
                {t('auth.verification.phoneOtp.resendOtp')}
              </Text>
            </Text>
          </View>

          {/* Verify Button */}
          <TouchableOpacity
            onPress={handleVerify}
            disabled={otp.some(digit => !digit)}
            className={`items-center justify-center w-full mb-8 rounded-lg h-14 ${
              otp.some(digit => !digit) ? 'bg-gray-300' : 'bg-primary'
            }`}
          >
            <Text className={`text-lg font-cairo-semibold ${
              otp.some(digit => !digit) ? 'text-gray-500' : 'text-black'
            }`}>
              {t('auth.verification.phoneOtp.verify')}
            </Text>
          </TouchableOpacity>
        </View>



        {/* Bottom Indicator */}
        <View className="items-center pb-8">
          <View className="w-32 h-1 bg-black rounded-full" />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
