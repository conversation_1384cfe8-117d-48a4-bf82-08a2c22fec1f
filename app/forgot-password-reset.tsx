import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function ForgotPasswordReset() {
  const { t } = useLanguage();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleCancel = () => {
    router.back();
  };

  const handleContinue = () => {
    if (password.trim() && confirmPassword.trim() && password === confirmPassword) {
      // Handle password reset logic here
      console.log('Password reset successful');
      // Navigate back to signin screen
      router.replace('/signin');
    }
  };

  const isFormValid = password.trim() && confirmPassword.trim() && password === confirmPassword;

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 px-6 pt-16">
            {/* Logo */}
            <View className="items-center mb-12">
              <Image
                source={require('../assets/images/logo.png')}
                className="w-40 h-16"
                resizeMode="contain"
              />
            </View>

            {/* Title and Subtitle */}
            <View className="mb-8">
              <Text className="text-2xl font-cairo-bold text-gray-900 mb-2">
                {t('auth.forgotPassword.resetTitle')}
              </Text>
              <Text className="text-base font-cairo-regular text-gray-500">
                {t('auth.forgotPassword.resetSubtitle')}
              </Text>
            </View>

            {/* Password Input */}
            <View className="mb-6">
              <View className="relative">
                <TextInput
                  className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                  placeholder={t('auth.forgotPassword.passwordPlaceholder')}
                  placeholderTextColor="#9CA3AF"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-4"
                >
                  <Ionicons
                    name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                    size={20}
                    color="#9CA3AF"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Confirm Password Input */}
            <View className="mb-8">
              <View className="relative">
                <TextInput
                  className="w-full h-14 px-4 pr-12 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
                  placeholder={t('auth.forgotPassword.confirmPasswordPlaceholder')}
                  placeholderTextColor="#9CA3AF"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-4"
                >
                  <Ionicons
                    name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                    size={20}
                    color="#9CA3AF"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Spacer to push buttons to bottom */}
            <View className="flex-1" />

            {/* Action Buttons */}
            <View className="flex-row space-x-4 mb-8">
              {/* Cancel Button */}
              <TouchableOpacity
                onPress={handleCancel}
                className="flex-1 h-14 bg-white border border-gray-300 rounded-lg items-center justify-center"
              >
                <Text className="font-cairo-medium text-gray-700 text-base">
                  {t('auth.forgotPassword.cancel')}
                </Text>
              </TouchableOpacity>

              {/* Continue Button */}
              <TouchableOpacity
                onPress={handleContinue}
                className="flex-1 h-14 bg-primary rounded-lg items-center justify-center"
                disabled={!isFormValid}
                style={{
                  opacity: isFormValid ? 1 : 0.5,
                }}
              >
                <Text className="font-cairo-medium text-black text-base">
                  {t('auth.forgotPassword.continue')}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Bottom Indicator */}
            <View className="items-center mb-6">
              <View className="w-32 h-1 bg-black rounded-full" />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
