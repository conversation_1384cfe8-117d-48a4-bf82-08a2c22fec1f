import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

const { width } = Dimensions.get('window');

export default function ForgotPasswordOTP() {
  const { t } = useLanguage();
  const { email } = useLocalSearchParams<{ email: string }>();
  const [otp, setOtp] = useState(['', '', '', '']);
  const [activeIndex, setActiveIndex] = useState(0);

  const handleNumberPress = (number: string) => {
    if (activeIndex < 4) {
      const newOtp = [...otp];
      newOtp[activeIndex] = number;
      setOtp(newOtp);
      setActiveIndex(activeIndex + 1);
    }
  };

  const handleBackspace = () => {
    if (activeIndex > 0) {
      const newOtp = [...otp];
      const targetIndex = activeIndex - 1;
      newOtp[targetIndex] = '';
      setOtp(newOtp);
      setActiveIndex(targetIndex);
    }
  };

  const handleVerify = () => {
    const otpCode = otp.join('');
    if (otpCode.length === 4) {
      // Navigate to reset password screen
      router.push('/forgot-password-reset');
    }
  };

  const handleResendOTP = () => {
    // Reset OTP and show resend logic
    setOtp(['', '', '', '']);
    setActiveIndex(0);
    console.log('Resending OTP to:', email);
  };

  const renderKeypadButton = (value: string, isSpecial = false) => (
    <TouchableOpacity
      key={value}
      onPress={() => {
        if (value === 'backspace') {
          handleBackspace();
        } else if (value !== '+*#' && value !== '') {
          handleNumberPress(value);
        }
      }}
      className="items-center justify-center bg-white rounded-lg shadow-sm"
      style={{
        width: (width - 60) / 3 - 8,
        height: 60,
        marginHorizontal: 4,
        marginVertical: 4,
      }}
      disabled={value === '+*#' || value === ''}
    >
      {value === 'backspace' ? (
        <Ionicons name="backspace-outline" size={24} color="#000" />
      ) : (
        <View className="items-center">
          <Text className="text-xl text-black font-cairo-bold">{value}</Text>
          {value === '2' && <Text className="text-xs text-gray-500 font-cairo-regular">ABC</Text>}
          {value === '3' && <Text className="text-xs text-gray-500 font-cairo-regular">DEF</Text>}
          {value === '4' && <Text className="text-xs text-gray-500 font-cairo-regular">GHI</Text>}
          {value === '5' && <Text className="text-xs text-gray-500 font-cairo-regular">JKL</Text>}
          {value === '6' && <Text className="text-xs text-gray-500 font-cairo-regular">MNO</Text>}
          {value === '7' && <Text className="text-xs text-gray-500 font-cairo-regular">PQRS</Text>}
          {value === '8' && <Text className="text-xs text-gray-500 font-cairo-regular">TUV</Text>}
          {value === '9' && <Text className="text-xs text-gray-500 font-cairo-regular">WXYZ</Text>}
          {value === '+*#' && <Text className="text-xs text-gray-500 font-cairo-regular">+*#</Text>}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="flex-1 px-6 pt-16">
        {/* Logo */}
        <View className="items-center mb-12">
          <Image
            source={require('../assets/images/logo.png')}
            className="w-40 h-16"
            resizeMode="contain"
          />
        </View>

        {/* Title and Subtitle */}
        <View className="mb-8">
          <Text className="mb-2 text-2xl text-gray-900 font-cairo-bold">
            {t('auth.forgotPassword.otpTitle')}
          </Text>
          <View className="flex-row items-center">
            <Text className="text-base text-gray-500 font-cairo-regular">
              {t('auth.forgotPassword.otpSubtitle')} 
            </Text>
            <Text className="ml-1 text-base text-black font-cairo-medium">
              {email}
            </Text>
            <TouchableOpacity className="ml-2">
              <Ionicons name="pencil" size={16} color="#000" />
            </TouchableOpacity>
          </View>
        </View>

        {/* OTP Input Boxes */}
        <View className="flex-row justify-center mb-6 space-x-4">
          {otp.map((digit, index) => (
            <View
              key={index}
              className={`w-16 h-16 rounded-lg border-2 items-center justify-center ${
                index === activeIndex ? 'border-primary bg-primary/10' : 'border-gray-300 bg-gray-50'
              }`}
            >
              <Text className="text-xl text-black font-cairo-bold">
                {digit}
              </Text>
            </View>
          ))}
        </View>

        {/* Resend OTP */}
        <View className="items-center mb-8">
          <Text className="text-gray-500 font-cairo-regular">
            {t('auth.forgotPassword.otpNotReceived')}{' '}
            <TouchableOpacity onPress={handleResendOTP}>
              <Text className="font-cairo-medium text-primary">
                {t('auth.forgotPassword.resendOTP')}
              </Text>
            </TouchableOpacity>
          </Text>
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          onPress={handleVerify}
          className="items-center justify-center w-full mb-8 rounded-lg h-14 bg-primary"
          disabled={otp.join('').length !== 4}
          style={{
            opacity: otp.join('').length === 4 ? 1 : 0.5,
          }}
        >
          <Text className="text-base text-black font-cairo-medium">
            {t('auth.forgotPassword.verify')}
          </Text>
        </TouchableOpacity>

        {/* Spacer */}
        <View className="flex-1" />

        {/* Custom Keypad */}
        <View className="px-4 py-6 bg-gray-100 rounded-t-3xl">
          <View className="flex-row flex-wrap justify-center">
            {/* Row 1 */}
            {renderKeypadButton('1')}
            {renderKeypadButton('2')}
            {renderKeypadButton('3')}
            
            {/* Row 2 */}
            {renderKeypadButton('4')}
            {renderKeypadButton('5')}
            {renderKeypadButton('6')}
            
            {/* Row 3 */}
            {renderKeypadButton('7')}
            {renderKeypadButton('8')}
            {renderKeypadButton('9')}
            
            {/* Row 4 */}
            {renderKeypadButton('+*#', true)}
            {renderKeypadButton('0')}
            {renderKeypadButton('backspace', true)}
          </View>
        </View>

        {/* Bottom Indicator */}
        <View className="items-center py-4">
          <View className="w-32 h-1 bg-black rounded-full" />
        </View>
      </View>
    </SafeAreaView>
  );
}
