import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function ForgotPassword() {
  const { t } = useLanguage();
  const [email, setEmail] = useState('');

  const handleCancel = () => {
    router.back();
  };

  const handleContinue = () => {
    if (email.trim()) {
      // Navigate to OTP verification screen
      router.push({
        pathname: '/forgot-password-otp',
        params: { email: email.trim() }
      });
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <View className="flex-1 px-6 pt-16">
          {/* Logo */}
          <View className="items-center mb-12">
            <Image
              source={require('../assets/images/logo.png')}
              className="w-40 h-16"
              resizeMode="contain"
            />
          </View>

          {/* Title and Subtitle */}
          <View className="mb-8">
            <Text className="text-2xl font-cairo-bold text-gray-900 mb-2">
              {t('auth.forgotPassword.title')}
            </Text>
            <Text className="text-base font-cairo-regular text-gray-500">
              {t('auth.forgotPassword.subtitle')}
            </Text>
          </View>

          {/* Email Input */}
          <View className="mb-8">
            <TextInput
              className="w-full h-14 px-4 bg-gray-50 rounded-lg font-cairo-regular text-base text-gray-900"
              placeholder={t('auth.forgotPassword.emailPlaceholder')}
              placeholderTextColor="#9CA3AF"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {/* Spacer to push buttons to bottom */}
          <View className="flex-1" />

          {/* Action Buttons */}
          <View className="flex-row space-x-4 mb-8">
            {/* Cancel Button */}
            <TouchableOpacity
              onPress={handleCancel}
              className="flex-1 h-14 bg-white border border-gray-300 rounded-lg items-center justify-center"
            >
              <Text className="font-cairo-medium text-gray-700 text-base">
                {t('auth.forgotPassword.cancel')}
              </Text>
            </TouchableOpacity>

            {/* Continue Button */}
            <TouchableOpacity
              onPress={handleContinue}
              className="flex-1 h-14 bg-primary rounded-lg items-center justify-center"
              disabled={!email.trim()}
              style={{
                opacity: email.trim() ? 1 : 0.5,
              }}
            >
              <Text className="font-cairo-medium text-black text-base">
                {t('auth.forgotPassword.continue')}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Bottom Indicator */}
          <View className="items-center mb-6">
            <View className="w-32 h-1 bg-black rounded-full" />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
