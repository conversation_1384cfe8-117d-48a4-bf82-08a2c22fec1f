import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import { useLanguage } from '@/hooks/useLanguage';

export default function EmailVerification() {
  const { t } = useLanguage();
  const params = useLocalSearchParams();
  const email = params.email as string;

  const handleResendEmail = () => {
    // Handle resend email logic here
    console.log('Resending verification email to:', email);
  };

  const handleVerifyEmail = () => {
    // Handle email verification logic here
    console.log('Email verification completed for:', email);
    // Navigate to success modal
    router.push('/verification-success' as any);
  };

  const handleEditEmail = () => {
    router.back();
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View className="flex-row items-center justify-between px-6 py-4">
          <TouchableOpacity onPress={() => router.back()} className="p-2">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
        </View>

        {/* Logo */}
        <View className="items-center px-6 pb-8">
          <Image
            source={require('@/assets/images/logo.png')}
            className="w-48 h-16"
            resizeMode="contain"
          />
        </View>

        {/* Content */}
        <View className="flex-1 px-6">
          {/* Email Verification Illustration */}
          <View className="items-center mb-8">
            <Image
              source={require('@/assets/images/auth/email-verification.png')}
              className="w-64 h-64"
              resizeMode="contain"
            />
          </View>

          {/* Title */}
          <Text className="mb-4 text-2xl text-center text-gray-900 font-cairo-bold">
            {t('auth.verification.emailVerification.title')}
          </Text>

          {/* Subtitle with email */}
          <View className="items-center mb-4">
            <Text className="mb-2 text-base text-center text-gray-600 font-cairo-regular">
              {t('auth.verification.emailVerification.subtitle')}
            </Text>
            <View className="flex-row items-center">
              <Text className="text-base text-gray-900 font-cairo-semibold">
                {email}
              </Text>
              <TouchableOpacity onPress={handleEditEmail} className="ml-2">
                <Ionicons name="pencil" size={16} color="#F59E0B" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Description */}
          <Text className="mb-8 text-sm text-center text-gray-500 font-cairo-regular">
            {t('auth.verification.emailVerification.description')}
          </Text>

          {/* Resend Email Button */}
          <TouchableOpacity
            onPress={handleVerifyEmail}
            className="items-center justify-center w-full mb-8 rounded-lg h-14 bg-primary"
          >
            <Text className="text-lg text-black font-cairo-semibold">
              {t('auth.verification.emailVerification.resendEmail')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Bottom Indicator */}
        <View className="items-center pb-8">
          <View className="w-32 h-1 bg-black rounded-full" />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
