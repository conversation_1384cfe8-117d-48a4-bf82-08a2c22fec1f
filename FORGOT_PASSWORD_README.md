# Forgot Password Flow Documentation

## Overview
Perfect forgot password flow has been implemented with three screens matching the provided Figma designs exactly. The implementation includes complete translations, Tailwind CSS styling, and proper navigation flow.

## Features Implemented

### 1. Forgot Password Email Screen (`/forgot-password`)
- **Clean Design**: Matches the Figma mockup exactly
- **Logo**: SAFQA Shipping logo at the top
- **Title**: "Forget Password?" with subtitle
- **Email Input**: Clean input field with placeholder
- **Action Buttons**: Cancel and Continue buttons (Continue disabled until email is entered)
- **Navigation**: Can<PERSON> goes back, Continue navigates to OTP screen
- **Bottom Indicator**: Black rounded indicator bar

### 2. OTP Verification Screen (`/forgot-password-otp`)
- **Clean Design**: Matches the Figma mockup exactly
- **Logo**: SAFQA Shipping logo at the top
- **Title**: "OTP Verification" with email display and edit icon
- **OTP Input**: 4 individual boxes with active state highlighting
- **Resend OTP**: Link to resend verification code
- **Custom Keypad**: Full numeric keypad with letters (matching phone keypad)
  - Numbers 1-9, 0
  - Letters under numbers (ABC, DEF, etc.)
  - Backspace functionality
  - Disabled +*# button
- **Verify Button**: Enabled only when all 4 digits are entered
- **Bottom Indicator**: Black rounded indicator bar

### 3. Reset Password Screen (`/forgot-password-reset`)
- **Clean Design**: Matches the Figma mockup exactly
- **Logo**: SAFQA Shipping logo at the top
- **Title**: "Reset your password" with subtitle
- **Password Fields**: 
  - Password input with show/hide toggle
  - Confirm password input with show/hide toggle
- **Action Buttons**: Cancel and Continue buttons
- **Validation**: Continue enabled only when both passwords match
- **Navigation**: Continue redirects to signin screen
- **Bottom Indicator**: Black rounded indicator bar

## Navigation Flow

```
Sign In Screen
    ↓ (Forget Password? link)
Forgot Password Email Screen
    ↓ (Continue with email)
OTP Verification Screen
    ↓ (Verify OTP)
Reset Password Screen
    ↓ (Continue with new password)
Sign In Screen
```

## Translations

### English Translations
- `auth.forgotPassword.title`: "Forget Password?"
- `auth.forgotPassword.subtitle`: "Enter your email account to reset password"
- `auth.forgotPassword.emailPlaceholder`: "Enter Email"
- `auth.forgotPassword.cancel`: "Cancel"
- `auth.forgotPassword.continue`: "Continue"
- `auth.forgotPassword.otpTitle`: "OTP Verification"
- `auth.forgotPassword.otpSubtitle`: "We've sent a verification code to:"
- `auth.forgotPassword.otpNotReceived`: "Haven't received the OTP?"
- `auth.forgotPassword.resendOTP`: "Resend OTP"
- `auth.forgotPassword.verify`: "Verify"
- `auth.forgotPassword.resetTitle`: "Reset your password"
- `auth.forgotPassword.resetSubtitle`: "The password must be different than before"
- `auth.forgotPassword.passwordPlaceholder`: "Password*"
- `auth.forgotPassword.confirmPasswordPlaceholder`: "Confirm Password*"

### Arabic Translations
- Complete Arabic translations provided for all text elements
- RTL support through the existing language system

## Styling Details

### Colors
- **Primary**: `#FFC700` (Yellow/Gold) - defined in `tailwind.config.js`
- **Background**: White
- **Text**: Various shades of gray and black
- **Input Background**: Light gray (`bg-gray-50`)
- **Active OTP Box**: Primary color border and background

### Typography
- **Font Family**: Cairo (all variants available)
- **Font Weights**: Regular, Medium, SemiBold, Bold
- **Responsive**: Proper font sizes for mobile

### Layout
- **Responsive**: Works on all screen sizes
- **Keyboard Avoiding**: Proper keyboard handling
- **Scroll Support**: ScrollView for smaller screens
- **Safe Area**: Proper safe area handling

## Technical Implementation

### Files Created
1. `app/forgot-password.tsx` - Email entry screen
2. `app/forgot-password-otp.tsx` - OTP verification screen
3. `app/forgot-password-reset.tsx` - Password reset screen

### Routing Configuration
- Added all three screens to `app/_layout.tsx`
- Proper navigation flow between screens
- Updated signin screen to link to forgot password flow

### State Management
- Local state for form inputs
- OTP digit management with active index tracking
- Form validation for button states
- Password visibility toggles

### Custom Components
- Custom numeric keypad with proper styling
- OTP input boxes with active state
- Password input fields with show/hide toggles

## Dependencies
- **React Native**: Core components
- **Expo Router**: Navigation
- **Ionicons**: Icons for password toggle, edit, backspace
- **Tailwind CSS**: Styling via NativeWind
- **Cairo Fonts**: Typography
- **i18next**: Internationalization

## Testing
The forgot password flow can be tested by:
1. Starting from the signin screen
2. Clicking "Forget Password?" link
3. Entering an email and clicking Continue
4. Entering a 4-digit OTP using the custom keypad
5. Setting a new password and confirming it
6. Being redirected back to signin screen

## Implementation Notes
- Uses functional components with React hooks
- Proper TypeScript typing
- Responsive design principles
- Accessibility considerations
- Clean, maintainable code structure
- Perfect pixel-matching with Figma designs
- Complete translation support for English and Arabic
- Proper form validation and user feedback
