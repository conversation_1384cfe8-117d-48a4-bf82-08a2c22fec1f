export interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
  translationKey: string;
}

export const COUNTRIES: Country[] = [
  { code: 'AF', name: 'Afghanistan', dialCode: '+93', flag: '🇦🇫', translationKey: 'auth.countries.af' },
  { code: 'AL', name: 'Albania', dialCode: '+355', flag: '🇦🇱', translationKey: 'auth.countries.al' },
  { code: 'DZ', name: 'Algeria', dialCode: '+213', flag: '🇩🇿', translationKey: 'auth.countries.dz' },
  { code: 'AS', name: 'American Samoa', dialCode: '+1‑684', flag: '🇦🇸', translationKey: 'auth.countries.as' },
  { code: 'AD', name: 'Andorra', dialCode: '+376', flag: '🇦🇩', translationKey: 'auth.countries.ad' },
  { code: 'AO', name: 'Angola', dialCode: '+244', flag: '🇦🇴', translationKey: 'auth.countries.ao' },
  { code: 'AR', name: 'Argentina', dialCode: '+54', flag: '🇦🇷', translationKey: 'auth.countries.ar' },
  { code: 'AM', name: 'Armenia', dialCode: '+374', flag: '🇦🇲', translationKey: 'auth.countries.am' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺', translationKey: 'auth.countries.au' },
  { code: 'AT', name: 'Austria', dialCode: '+43', flag: '🇦🇹', translationKey: 'auth.countries.at' },
  { code: 'AZ', name: 'Azerbaijan', dialCode: '+994', flag: '🇦🇿', translationKey: 'auth.countries.az' },
  { code: 'BH', name: 'Bahrain', dialCode: '+973', flag: '🇧🇭', translationKey: 'auth.countries.bh' },
  { code: 'BD', name: 'Bangladesh', dialCode: '+880', flag: '🇧🇩', translationKey: 'auth.countries.bd' },
  { code: 'BY', name: 'Belarus', dialCode: '+375', flag: '🇧🇾', translationKey: 'auth.countries.by' },
  { code: 'BE', name: 'Belgium', dialCode: '+32', flag: '🇧🇪', translationKey: 'auth.countries.be' },
  { code: 'BJ', name: 'Benin', dialCode: '+229', flag: '🇧🇯', translationKey: 'auth.countries.bj' },
  { code: 'BT', name: 'Bhutan', dialCode: '+975', flag: '🇧🇹', translationKey: 'auth.countries.bt' },
  { code: 'BO', name: 'Bolivia', dialCode: '+591', flag: '🇧🇴', translationKey: 'auth.countries.bo' },
  { code: 'BA', name: 'Bosnia & Herzegovina', dialCode: '+387', flag: '🇧🇦', translationKey: 'auth.countries.ba' },
  { code: 'BW', name: 'Botswana', dialCode: '+267', flag: '🇧🇼', translationKey: 'auth.countries.bw' },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷', translationKey: 'auth.countries.br' },
  { code: 'BN', name: 'Brunei', dialCode: '+673', flag: '🇧🇳', translationKey: 'auth.countries.bn' },
  { code: 'BG', name: 'Bulgaria', dialCode: '+359', flag: '🇧🇬', translationKey: 'auth.countries.bg' },
  { code: 'BF', name: 'Burkina Faso', dialCode: '+226', flag: '🇧🇫', translationKey: 'auth.countries.bf' },
  { code: 'BI', name: 'Burundi', dialCode: '+257', flag: '🇧🇮', translationKey: 'auth.countries.bi' },
  { code: 'KH', name: 'Cambodia', dialCode: '+855', flag: '🇰🇭', translationKey: 'auth.countries.kh' },
  { code: 'CM', name: 'Cameroon', dialCode: '+237', flag: '🇨🇲', translationKey: 'auth.countries.cm' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦', translationKey: 'auth.countries.ca' },
  { code: 'CV', name: 'Cape Verde', dialCode: '+238', flag: '🇨🇻', translationKey: 'auth.countries.cv' },
  { code: 'CF', name: 'Central African Republic', dialCode: '+236', flag: '🇨🇫', translationKey: 'auth.countries.cf' },
  { code: 'TD', name: 'Chad', dialCode: '+235', flag: '🇹🇩', translationKey: 'auth.countries.td' },
  { code: 'CL', name: 'Chile', dialCode: '+56', flag: '🇨🇱', translationKey: 'auth.countries.cl' },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳', translationKey: 'auth.countries.cn' },
  { code: 'CO', name: 'Colombia', dialCode: '+57', flag: '🇨🇴', translationKey: 'auth.countries.co' },
  { code: 'KM', name: 'Comoros', dialCode: '+269', flag: '🇰🇲', translationKey: 'auth.countries.km' },
  { code: 'CD', name: 'Congo (DRC)', dialCode: '+243', flag: '🇨🇩', translationKey: 'auth.countries.cd' },
  { code: 'CG', name: 'Congo (Republic)', dialCode: '+242', flag: '🇨🇬', translationKey: 'auth.countries.cg' },
  { code: 'CR', name: 'Costa Rica', dialCode: '+506', flag: '🇨🇷', translationKey: 'auth.countries.cr' },
  { code: 'CI', name: 'Côte d’Ivoire', dialCode: '+225', flag: '🇨🇮', translationKey: 'auth.countries.ci' },
  { code: 'HR', name: 'Croatia', dialCode: '+385', flag: '🇭🇷', translationKey: 'auth.countries.hr' },
  { code: 'CY', name: 'Cyprus', dialCode: '+357', flag: '🇨🇾', translationKey: 'auth.countries.cy' },
  { code: 'CZ', name: 'Czech Republic', dialCode: '+420', flag: '🇨🇿', translationKey: 'auth.countries.cz' },
  { code: 'DK', name: 'Denmark', dialCode: '+45', flag: '🇩🇰', translationKey: 'auth.countries.dk' },
  { code: 'DJ', name: 'Djibouti', dialCode: '+253', flag: '🇩🇯', translationKey: 'auth.countries.dj' },
  { code: 'DO', name: 'Dominican Republic', dialCode: '+1‑809', flag: '🇩🇴', translationKey: 'auth.countries.do' },
  { code: 'EC', name: 'Ecuador', dialCode: '+593', flag: '🇪🇨', translationKey: 'auth.countries.ec' },
  { code: 'EG', name: 'Egypt', dialCode: '+20', flag: '🇪🇬', translationKey: 'auth.countries.eg' },
  { code: 'SV', name: 'El Salvador', dialCode: '+503', flag: '🇸🇻', translationKey: 'auth.countries.sv' },
  { code: 'GQ', name: 'Equatorial Guinea', dialCode: '+240', flag: '🇬🇶', translationKey: 'auth.countries.gq' },
  { code: 'ER', name: 'Eritrea', dialCode: '+291', flag: '🇪🇷', translationKey: 'auth.countries.er' },
  { code: 'EE', name: 'Estonia', dialCode: '+372', flag: '🇪🇪', translationKey: 'auth.countries.ee' },
  { code: 'SZ', name: 'Eswatini', dialCode: '+268', flag: '🇸🇿', translationKey: 'auth.countries.sz' },
  { code: 'ET', name: 'Ethiopia', dialCode: '+251', flag: '🇪🇹', translationKey: 'auth.countries.et' },
  { code: 'FJ', name: 'Fiji', dialCode: '+679', flag: '🇫🇯', translationKey: 'auth.countries.fj' },
  { code: 'FI', name: 'Finland', dialCode: '+358', flag: '🇫🇮', translationKey: 'auth.countries.fi' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷', translationKey: 'auth.countries.fr' },
  { code: 'GA', name: 'Gabon', dialCode: '+241', flag: '🇬🇦', translationKey: 'auth.countries.ga' },
  { code: 'GM', name: 'Gambia', dialCode: '+220', flag: '🇬🇲', translationKey: 'auth.countries.gm' },
  { code: 'GE', name: 'Georgia', dialCode: '+995', flag: '🇬🇪', translationKey: 'auth.countries.ge' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪', translationKey: 'auth.countries.de' },
  { code: 'GH', name: 'Ghana', dialCode: '+233', flag: '🇬🇭', translationKey: 'auth.countries.gh' },
  { code: 'GR', name: 'Greece', dialCode: '+30', flag: '🇬🇷', translationKey: 'auth.countries.gr' },
  { code: 'GU', name: 'Guam', dialCode: '+1‑671', flag: '🇬🇺', translationKey: 'auth.countries.gu' },
  { code: 'GT', name: 'Guatemala', dialCode: '+502', flag: '🇬🇹', translationKey: 'auth.countries.gt' },
  { code: 'GN', name: 'Guinea', dialCode: '+224', flag: '🇬🇳', translationKey: 'auth.countries.gn' },
  { code: 'GW', name: 'Guinea‑Bissau', dialCode: '+245', flag: '🇬🇼', translationKey: 'auth.countries.gw' },
  { code: 'GY', name: 'Guyana', dialCode: '+592', flag: '🇬🇾', translationKey: 'auth.countries.gy' },
  { code: 'HT', name: 'Haiti', dialCode: '+509', flag: '🇭🇹', translationKey: 'auth.countries.ht' },
  { code: 'HN', name: 'Honduras', dialCode: '+504', flag: '🇭🇳', translationKey: 'auth.countries.hn' },
  { code: 'HK', name: 'Hong Kong SAR', dialCode: '+852', flag: '🇭🇰', translationKey: 'auth.countries.hk' },
  { code: 'HU', name: 'Hungary', dialCode: '+36', flag: '🇭🇺', translationKey: 'auth.countries.hu' },
  { code: 'IS', name: 'Iceland', dialCode: '+354', flag: '🇮🇸', translationKey: 'auth.countries.is' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳', translationKey: 'auth.countries.in' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62', flag: '🇮🇩', translationKey: 'auth.countries.id' },
  { code: 'IR', name: 'Iran', dialCode: '+98', flag: '🇮🇷', translationKey: 'auth.countries.ir' },
  { code: 'IQ', name: 'Iraq', dialCode: '+964', flag: '🇮🇶', translationKey: 'auth.countries.iq' },
  { code: 'IE', name: 'Ireland', dialCode: '+353', flag: '🇮🇪', translationKey: 'auth.countries.ie' },
  { code: 'IL', name: 'Israel', dialCode: '+972', flag: '🇮🇱', translationKey: 'auth.countries.il' },
  { code: 'IT', name: 'Italy', dialCode: '+39', flag: '🇮🇹', translationKey: 'auth.countries.it' },
  { code: 'JM', name: 'Jamaica', dialCode: '+1‑876', flag: '🇯🇲', translationKey: 'auth.countries.jm' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵', translationKey: 'auth.countries.jp' },
  { code: 'JO', name: 'Jordan', dialCode: '+962', flag: '🇯🇴', translationKey: 'auth.countries.jo' },
  { code: 'KZ', name: 'Kazakhstan', dialCode: '+7', flag: '🇰🇿', translationKey: 'auth.countries.kz' },
  { code: 'KE', name: 'Kenya', dialCode: '+254', flag: '🇰🇪', translationKey: 'auth.countries.ke' },
  { code: 'KI', name: 'Kiribati', dialCode: '+686', flag: '🇰🇮', translationKey: 'auth.countries.ki' },
  { code: 'KW', name: 'Kuwait', dialCode: '+965', flag: '🇰🇼', translationKey: 'auth.countries.kw' },
  { code: 'KG', name: 'Kyrgyzstan', dialCode: '+996', flag: '🇰🇬', translationKey: 'auth.countries.kg' },
  { code: 'LA', name: 'Laos', dialCode: '+856', flag: '🇱🇦', translationKey: 'auth.countries.la' },
  { code: 'LV', name: 'Latvia', dialCode: '+371', flag: '🇱🇻', translationKey: 'auth.countries.lv' },
  { code: 'LB', name: 'Lebanon', dialCode: '+961', flag: '🇱🇧', translationKey: 'auth.countries.lb' },
  { code: 'LS', name: 'Lesotho', dialCode: '+266', flag: '🇱🇸', translationKey: 'auth.countries.ls' },
  { code: 'LR', name: 'Liberia', dialCode: '+231', flag: '🇱🇷', translationKey: 'auth.countries.lr' },
  { code: 'LY', name: 'Libya', dialCode: '+218', flag: '🇱🇾', translationKey: 'auth.countries.ly' },
  { code: 'LI', name: 'Liechtenstein', dialCode: '+423', flag: '🇱🇮', translationKey: 'auth.countries.li' },
  { code: 'LT', name: 'Lithuania', dialCode: '+370', flag: '🇱🇹', translationKey: 'auth.countries.lt' },
  { code: 'LU', name: 'Luxembourg', dialCode: '+352', flag: '🇱🇺', translationKey: 'auth.countries.lu' },
  { code: 'MO', name: 'Macau SAR', dialCode: '+853', flag: '🇲🇴', translationKey: 'auth.countries.mo' },
  { code: 'MK', name: 'North Macedonia', dialCode: '+389', flag: '🇲🇰', translationKey: 'auth.countries.mk' },
  { code: 'MG', name: 'Madagascar', dialCode: '+261', flag: '🇲🇬', translationKey: 'auth.countries.mg' },
  { code: 'MW', name: 'Malawi', dialCode: '+265', flag: '🇲🇼', translationKey: 'auth.countries.mw' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60', flag: '🇲🇾', translationKey: 'auth.countries.my' },
  { code: 'MV', name: 'Maldives', dialCode: '+960', flag: '🇲🇻', translationKey: 'auth.countries.mv' },
  { code: 'ML', name: 'Mali', dialCode: '+223', flag: '🇲🇱', translationKey: 'auth.countries.ml' },
  { code: 'MT', name: 'Malta', dialCode: '+356', flag: '🇲🇹', translationKey: 'auth.countries.mt' },
  { code: 'MH', name: 'Marshall Islands', dialCode: '+692', flag: '🇲🇭', translationKey: 'auth.countries.mh' },
  { code: 'MR', name: 'Mauritania', dialCode: '+222', flag: '🇲🇷', translationKey: 'auth.countries.mr' },
  { code: 'MU', name: 'Mauritius', dialCode: '+230', flag: '🇲🇺', translationKey: 'auth.countries.mu' },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽', translationKey: 'auth.countries.mx' },
  { code: 'FM', name: 'Micronesia', dialCode: '+691', flag: '🇫🇲', translationKey: 'auth.countries.fm' },
  { code: 'MD', name: 'Moldova', dialCode: '+373', flag: '🇲🇩', translationKey: 'auth.countries.md' },
  { code: 'MC', name: 'Monaco', dialCode: '+377', flag: '🇲🇨', translationKey: 'auth.countries.mc' },
  { code: 'MN', name: 'Mongolia', dialCode: '+976', flag: '🇲🇳', translationKey: 'auth.countries.mn' },
  { code: 'ME', name: 'Montenegro', dialCode: '+382', flag: '🇲🇪', translationKey: 'auth.countries.me' },
  { code: 'MA', name: 'Morocco', dialCode: '+212', flag: '🇲🇦', translationKey: 'auth.countries.ma' },
  { code: 'MZ', name: 'Mozambique', dialCode: '+258', flag: '🇲🇿', translationKey: 'auth.countries.mz' },
  { code: 'MM', name: 'Myanmar', dialCode: '+95', flag: '🇲🇲', translationKey: 'auth.countries.mm' },
  { code: 'NA', name: 'Namibia', dialCode: '+264', flag: '🇳🇦', translationKey: 'auth.countries.na' },
  { code: 'NR', name: 'Nauru', dialCode: '+674', flag: '🇳🇷', translationKey: 'auth.countries.nr' },
  { code: 'NP', name: 'Nepal', dialCode: '+977', flag: '🇳🇵', translationKey: 'auth.countries.np' },
  { code: 'NL', name: 'Netherlands', dialCode: '+31', flag: '🇳🇱', translationKey: 'auth.countries.nl' },
  { code: 'NZ', name: 'New Zealand', dialCode: '+64', flag: '🇳🇿', translationKey: 'auth.countries.nz' },
  { code: 'NI', name: 'Nicaragua', dialCode: '+505', flag: '🇳🇮', translationKey: 'auth.countries.ni' },
  { code: 'NE', name: 'Niger', dialCode: '+227', flag: '🇳🇪', translationKey: 'auth.countries.ne' },
  { code: 'NG', name: 'Nigeria', dialCode: '+234', flag: '🇳🇬', translationKey: 'auth.countries.ng' },
  { code: 'KP', name: 'North Korea', dialCode: '+850', flag: '🇰🇵', translationKey: 'auth.countries.kp' },
  { code: 'NO', name: 'Norway', dialCode: '+47', flag: '🇳🇴', translationKey: 'auth.countries.no' },
  { code: 'OM', name: 'Oman', dialCode: '+968', flag: '🇴🇲', translationKey: 'auth.countries.om' },
  { code: 'PK', name: 'Pakistan', dialCode: '+92', flag: '🇵🇰', translationKey: 'auth.countries.pk' },
  { code: 'PW', name: 'Palau', dialCode: '+680', flag: '🇵🇼', translationKey: 'auth.countries.pw' },
  { code: 'PS', name: 'Palestinian Territories', dialCode: '+970', flag: '🇵🇸', translationKey: 'auth.countries.ps' },
  { code: 'PA', name: 'Panama', dialCode: '+507', flag: '🇵🇦', translationKey: 'auth.countries.pa' },
  { code: 'PG', name: 'Papua New Guinea', dialCode: '+675', flag: '🇵🇬', translationKey: 'auth.countries.pg' },
  { code: 'PY', name: 'Paraguay', dialCode: '+595', flag: '🇵🇾', translationKey: 'auth.countries.py' },
  { code: 'PE', name: 'Peru', dialCode: '+51', flag: '🇵🇪', translationKey: 'auth.countries.pe' },
  { code: 'PH', name: 'Philippines', dialCode: '+63', flag: '🇵🇭', translationKey: 'auth.countries.ph' },
  { code: 'PL', name: 'Poland', dialCode: '+48', flag: '🇵🇱', translationKey: 'auth.countries.pl' },
  { code: 'PT', name: 'Portugal', dialCode: '+351', flag: '🇵🇹', translationKey: 'auth.countries.pt' },
  { code: 'QA', name: 'Qatar', dialCode: '+974', flag: '🇶🇦', translationKey: 'auth.countries.qa' },
  { code: 'RO', name: 'Romania', dialCode: '+40', flag: '🇷🇴', translationKey: 'auth.countries.ro' },
  { code: 'RU', name: 'Russia', dialCode: '+7', flag: '🇷🇺', translationKey: 'auth.countries.ru' },
  { code: 'RW', name: 'Rwanda', dialCode: '+250', flag: '🇷🇼', translationKey: 'auth.countries.rw' },
  { code: 'WS', name: 'Samoa', dialCode: '+685', flag: '🇼🇸', translationKey: 'auth.countries.ws' },
  { code: 'SM', name: 'San Marino', dialCode: '+378', flag: '🇸🇲', translationKey: 'auth.countries.sm' },
  { code: 'ST', name: 'São Tomé & Príncipe', dialCode: '+239', flag: '🇸🇹', translationKey: 'auth.countries.st' },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966', flag: '🇸🇦', translationKey: 'auth.countries.sa' },
  { code: 'SN', name: 'Senegal', dialCode: '+221', flag: '🇸🇳', translationKey: 'auth.countries.sn' },
  { code: 'RS', name: 'Serbia', dialCode: '+381', flag: '🇷🇸', translationKey: 'auth.countries.rs' },
  { code: 'SC', name: 'Seychelles', dialCode: '+248', flag: '🇸🇨', translationKey: 'auth.countries.sc' },
  { code: 'SL', name: 'Sierra Leone', dialCode: '+232', flag: '🇸🇱', translationKey: 'auth.countries.sl' },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬', translationKey: 'auth.countries.sg' },
  { code: 'SK', name: 'Slovakia', dialCode: '+421', flag: '🇸🇰', translationKey: 'auth.countries.sk' },
  { code: 'SI', name: 'Slovenia', dialCode: '+386', flag: '🇸🇮', translationKey: 'auth.countries.si' },
  { code: 'SB', name: 'Solomon Islands', dialCode: '+677', flag: '🇸🇧', translationKey: 'auth.countries.sb' },
  { code: 'SO', name: 'Somalia', dialCode: '+252', flag: '🇸🇴', translationKey: 'auth.countries.so' },
  { code: 'ZA', name: 'South Africa', dialCode: '+27', flag: '🇿🇦', translationKey: 'auth.countries.za' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷', translationKey: 'auth.countries.kr' },
  { code: 'SS', name: 'South Sudan', dialCode: '+211', flag: '🇸🇸', translationKey: 'auth.countries.ss' },
  { code: 'ES', name: 'Spain', dialCode: '+34', flag: '🇪🇸', translationKey: 'auth.countries.es' },
  { code: 'LK', name: 'Sri Lanka', dialCode: '+94', flag: '🇱🇰', translationKey: 'auth.countries.lk' },
  { code: 'SD', name: 'Sudan', dialCode: '+249', flag: '🇸🇩', translationKey: 'auth.countries.sd' },
  { code: 'SR', name: 'Suriname', dialCode: '+597', flag: '🇸🇷', translationKey: 'auth.countries.sr' },
  { code: 'SE', name: 'Sweden', dialCode: '+46', flag: '🇸🇪', translationKey: 'auth.countries.se' },
  { code: 'CH', name: 'Switzerland', dialCode: '+41', flag: '🇨🇭', translationKey: 'auth.countries.ch' },
  { code: 'SY', name: 'Syria', dialCode: '+963', flag: '🇸🇾', translationKey: 'auth.countries.sy' },
  { code: 'TW', name: 'Taiwan', dialCode: '+886', flag: '🇹🇼', translationKey: 'auth.countries.tw' },
  { code: 'TJ', name: 'Tajikistan', dialCode: '+992', flag: '🇹🇯', translationKey: 'auth.countries.tj' },
  { code: 'TZ', name: 'Tanzania', dialCode: '+255', flag: '🇹🇿', translationKey: 'auth.countries.tz' },
  { code: 'TH', name: 'Thailand', dialCode: '+66', flag: '🇹🇭', translationKey: 'auth.countries.th' },
  { code: 'TL', name: 'Timor‑Leste', dialCode: '+670', flag: '🇹🇱', translationKey: 'auth.countries.tl' },
  { code: 'TG', name: 'Togo', dialCode: '+228', flag: '🇹🇬', translationKey: 'auth.countries.tg' },
  { code: 'TO', name: 'Tonga', dialCode: '+676', flag: '🇹🇴', translationKey: 'auth.countries.to' },
  { code: 'TN', name: 'Tunisia', dialCode: '+216', flag: '🇹🇳', translationKey: 'auth.countries.tn' },
  { code: 'TR', name: 'Turkey', dialCode: '+90', flag: '🇹🇷', translationKey: 'auth.countries.tr' },
  { code: 'TM', name: 'Turkmenistan', dialCode: '+993', flag: '🇹🇲', translationKey: 'auth.countries.tm' },
  { code: 'TV', name: 'Tuvalu', dialCode: '+688', flag: '🇹🇻', translationKey: 'auth.countries.tv' },
  { code: 'UG', name: 'Uganda', dialCode: '+256', flag: '🇺🇬', translationKey: 'auth.countries.ug' },
  { code: 'UA', name: 'Ukraine', dialCode: '+380', flag: '🇺🇦', translationKey: 'auth.countries.ua' },
  { code: 'AE', name: 'United Arab Emirates', dialCode: '+971', flag: '🇦🇪', translationKey: 'auth.countries.ae' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧', translationKey: 'auth.countries.gb' },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸', translationKey: 'auth.countries.us' },
  { code: 'UY', name: 'Uruguay', dialCode: '+598', flag: '🇺🇾', translationKey: 'auth.countries.uy' },
  { code: 'UZ', name: 'Uzbekistan', dialCode: '+998', flag: '🇺🇿', translationKey: 'auth.countries.uz' },
  { code: 'VU', name: 'Vanuatu', dialCode: '+678', flag: '🇻🇺', translationKey: 'auth.countries.vu' },
  { code: 'VA', name: 'Vatican City', dialCode: '+379', flag: '🇻🇦', translationKey: 'auth.countries.va' },
  { code: 'VE', name: 'Venezuela', dialCode: '+58', flag: '🇻🇪', translationKey: 'auth.countries.ve' },
  { code: 'VN', name: 'Vietnam', dialCode: '+84', flag: '🇻🇳', translationKey: 'auth.countries.vn' },
  { code: 'YE', name: 'Yemen', dialCode: '+967', flag: '🇾🇪', translationKey: 'auth.countries.ye' },
  { code: 'ZM', name: 'Zambia', dialCode: '+260', flag: '🇿🇲', translationKey: 'auth.countries.zm' },
  { code: 'ZW', name: 'Zimbabwe', dialCode: '+263', flag: '🇿🇼', translationKey: 'auth.countries.zw' },
];

export const DEFAULT_COUNTRY = COUNTRIES.find(country => country.code === 'SA') || COUNTRIES[0]; // Saudi Arabia as default
